import { useQuery } from "@tanstack/react-query";
import {
  ActivityIcon,
  BarChart3Icon,
  ClockArrowDownIcon,
  ClockArrowUpIcon,
  CoinsIcon,
  DollarSignIcon,
  RefreshCwIcon,
} from "lucide-react";
import { useState } from "react";
import { Area, AreaChart, Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@/components/ui/chart";
import { Icons } from "@/components/ui/icons";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { useTRPC } from "@/lib/trpc/client";
import { LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";
import { format } from "@/shared/utils";

type SortOption = "oldest" | "newest" | "most_expensive" | "most_tokens";

// Chart configurations
const requestsChartConfig = {
  totalRequests: {
    label: "Requests",
    color: "hsl(var(--chart-1))",
  },
} satisfies ChartConfig;

const tokensChartConfig = {
  totalInputTokens: {
    label: "Input Tokens",
    color: "hsl(var(--chart-1))",
  },
  totalOutputTokens: {
    label: "Output Tokens",
    color: "hsl(var(--chart-2))",
  },
  totalReasoningTokens: {
    label: "Reasoning Tokens",
    color: "hsl(var(--chart-3))",
  },
} satisfies ChartConfig;

const costChartConfig = {
  totalCost: {
    label: "Cost",
    color: "hsl(var(--chart-4))",
  },
} satisfies ChartConfig;

export function AdminLogsPage() {
  const [selectedProvider, setSelectedProvider] = useState<LLM_Providers | "all">("all");
  const [selectedModelFamily, setSelectedModelFamily] = useState<string>("all");
  const [selectedApiKey, setSelectedApiKey] = useState<string>("all");
  const [sortBy, setSortBy] = useState<SortOption>("newest");
  const [currentPage, setCurrentPage] = useState(1);
  const [chartDays, setChartDays] = useState(30);

  const trpc = useTRPC();

  // Get filter options
  const { data: filterOptions } = useQuery(trpc.logs.getFilterOptions.queryOptions());

  // Get summary statistics
  const { data: summary } = useQuery(trpc.logs.getSummary.queryOptions());

  // Get daily usage for charts
  const { data: dailyUsage } = useQuery(
    trpc.logs.getDailyUsage.queryOptions({
      days: chartDays,
      provider: selectedProvider !== "all" ? selectedProvider : undefined,
      modelFamilyId: selectedModelFamily !== "all" ? selectedModelFamily : undefined,
    }),
  );

  // Get logs data
  const {
    data: logsData,
    isLoading,
    refetch,
  } = useQuery(
    trpc.logs.list.queryOptions({
      provider: selectedProvider !== "all" ? selectedProvider : undefined,
      modelFamilyId: selectedModelFamily !== "all" ? selectedModelFamily : undefined,
      keyHash: selectedApiKey !== "all" ? selectedApiKey : undefined,
      sortBy,
      page: currentPage,
      pageSize: 20,
    }),
  );

  const handleRefresh = () => {
    refetch();
  };

  const getSortIcon = (option: SortOption) => {
    switch (option) {
      case "oldest":
        return <ClockArrowUpIcon className="h-4 w-4" />;
      case "newest":
        return <ClockArrowDownIcon className="h-4 w-4" />;
      case "most_expensive":
        return <DollarSignIcon className="h-4 w-4" />;
      case "most_tokens":
        return <CoinsIcon className="h-4 w-4" />;
      default:
        return <ClockArrowDownIcon className="h-4 w-4" />;
    }
  };

  const getSortLabel = (option: SortOption) => {
    switch (option) {
      case "oldest":
        return "Oldest";
      case "newest":
        return "Newest";
      case "most_expensive":
        return "Most Expensive";
      case "most_tokens":
        return "Most Tokens";
      default:
        return "Newest";
    }
  };

  const formatChartDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (value: number) => {
    return `$${value.toFixed(6)}`;
  };

  return (
    <div className="space-y-2 p-2">
      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-4">
          <Card className="gap-2 py-4">
            <CardHeader className="flex flex-row items-center justify-between pb-0">
              <CardTitle className="text-lg font-medium">Total Requests</CardTitle>
              <ActivityIcon className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{format.number(summary.totalLogs)}</div>
            </CardContent>
          </Card>

          <Card className="gap-2 py-4">
            <CardHeader className="flex flex-row items-center justify-between pb-0">
              <CardTitle className="text-lg font-medium">Total Tokens</CardTitle>
              <CoinsIcon className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{format.number(summary.totalTokens)}</div>
            </CardContent>
          </Card>

          <Card className="gap-2 py-4">
            <CardHeader className="flex flex-row items-center justify-between pb-0">
              <CardTitle className="text-lg font-medium">Total Cost</CardTitle>
              <DollarSignIcon className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{format.costShort(summary.totalCost)}</div>
            </CardContent>
          </Card>

          <Card className="gap-2 py-4">
            <CardHeader className="flex flex-row items-center justify-between pb-0">
              <CardTitle className="text-lg font-medium">Avg Tokens/Request</CardTitle>
              <BarChart3Icon className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {format.number(Math.round(summary.avgTokensPerRequest))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Daily Usage Charts */}
      {dailyUsage && dailyUsage.length > 0 && (
        <div className="space-y-4">
          {/* Chart Controls */}
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Usage Analytics</h2>
            <Select
              value={chartDays.toString()}
              onValueChange={(value) => setChartDays(Number(value))}
            >
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="14">Last 14 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="60">Last 60 days</SelectItem>
                <SelectItem value="90">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
            {/* Requests Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ActivityIcon className="h-5 w-5" />
                  Daily Requests
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={requestsChartConfig} className="min-h-[300px] w-full">
                  <AreaChart accessibilityLayer data={dailyUsage}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={formatChartDate}
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                    />
                    <YAxis fontSize={12} tickLine={false} axisLine={false} />
                    <ChartTooltip
                      content={
                        <ChartTooltipContent
                          labelFormatter={(value) => formatChartDate(value as string)}
                        />
                      }
                    />
                    <Area
                      type="monotone"
                      dataKey="totalRequests"
                      stroke="var(--color-totalRequests)"
                      fill="var(--color-totalRequests)"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ChartContainer>
              </CardContent>
            </Card>

            {/* Tokens Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CoinsIcon className="h-5 w-5" />
                  Daily Token Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={tokensChartConfig} className="min-h-[300px] w-full">
                  <BarChart accessibilityLayer data={dailyUsage}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={formatChartDate}
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                    />
                    <YAxis fontSize={12} tickLine={false} axisLine={false} />
                    <ChartTooltip
                      content={
                        <ChartTooltipContent
                          labelFormatter={(value) => formatChartDate(value as string)}
                        />
                      }
                    />
                    <Bar
                      dataKey="totalInputTokens"
                      stackId="tokens"
                      fill="var(--color-totalInputTokens)"
                    />
                    <Bar
                      dataKey="totalOutputTokens"
                      stackId="tokens"
                      fill="var(--color-totalOutputTokens)"
                    />
                    <Bar
                      dataKey="totalReasoningTokens"
                      stackId="tokens"
                      fill="var(--color-totalReasoningTokens)"
                    />
                  </BarChart>
                </ChartContainer>
              </CardContent>
            </Card>

            {/* Cost Chart */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSignIcon className="h-5 w-5" />
                  Daily Cost
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={costChartConfig} className="min-h-[300px] w-full">
                  <AreaChart accessibilityLayer data={dailyUsage}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={formatChartDate}
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                    />
                    <YAxis fontSize={12} tickLine={false} axisLine={false} />
                    <ChartTooltip
                      content={
                        <ChartTooltipContent
                          labelFormatter={(value) => formatChartDate(value as string)}
                          formatter={(value) => [formatCurrency(Number(value)), "Cost"]}
                        />
                      }
                    />
                    <Area
                      type="monotone"
                      dataKey="totalCost"
                      stroke="var(--color-totalCost)"
                      fill="var(--color-totalCost)"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-wrap items-center gap-2">
          {/* Provider Filter */}
          <Select
            value={selectedProvider}
            onValueChange={(value) => setSelectedProvider(value as LLM_Providers | "all")}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Providers" />
            </SelectTrigger>

            <SelectContent>
              <SelectItem value="all">All Providers</SelectItem>
              {filterOptions?.providers.map((provider) => (
                <SelectItem key={provider} value={provider}>
                  <div className="flex items-center gap-2">
                    <Icons.provider provider={provider} className="h-4 w-4" />
                    {LLM_PROVIDER_DISPLAY_NAME[provider]}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Model Family Filter */}
          <Select value={selectedModelFamily} onValueChange={setSelectedModelFamily}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="All Model Families" />
            </SelectTrigger>

            <SelectContent>
              <SelectItem value="all">All Model Families</SelectItem>
              {filterOptions?.modelFamilies.map((family) => (
                <SelectItem key={family.id} value={family.id}>
                  <div className="flex items-center gap-2">
                    <Icons.provider provider={family.provider} className="h-4 w-4" />
                    {family.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* API Key Filter */}
          <Select value={selectedApiKey} onValueChange={setSelectedApiKey}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="All API Keys" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All API Keys</SelectItem>
              {filterOptions?.apiKeys.map((key) => (
                <SelectItem key={key.hash} value={key.hash}>
                  {key.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Sort Filter */}
          <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortOption)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {(["newest", "oldest", "most_expensive", "most_tokens"] as const).map((option) => (
                <SelectItem key={option} value={option}>
                  <div className="flex items-center gap-2">
                    {getSortIcon(option)}
                    {getSortLabel(option)}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Refresh Button */}
          <Button variant="outline" onClick={handleRefresh} title="Refresh logs">
            <RefreshCwIcon className="h-4 w-4" />
          </Button>

          {/* Clear Filters Button */}
          <Button
            variant="outline"
            onClick={() => {
              setSelectedProvider("all");
              setSelectedModelFamily("all");
              setSelectedApiKey("all");
              setSortBy("newest");
              setCurrentPage(1);
            }}
            className={
              selectedProvider !== "all" ||
              selectedModelFamily !== "all" ||
              selectedApiKey !== "all" ||
              sortBy !== "newest"
                ? ""
                : "hidden"
            }
          >
            Clear Filters
          </Button>
        </div>
      </div>

      {/* Logs Table */}
      <Card className="p-0">
        <CardContent className="p-2">
          {isLoading ? (
            <div className="flex h-24 items-center justify-center">
              <div className="text-muted-foreground">Loading logs...</div>
            </div>
          ) : !logsData?.logs.length ? (
            <div className="flex h-24 items-center justify-center">
              <div className="text-muted-foreground">No logs found matching your filters.</div>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Provider</TableHead>
                    <TableHead>Model Family</TableHead>
                    <TableHead>Model</TableHead>
                    <TableHead className="text-right">Input</TableHead>
                    <TableHead className="text-right">Output</TableHead>
                    <TableHead className="text-right">Reasoning</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                    <TableHead className="text-right">Cost</TableHead>
                    <TableHead>API Key</TableHead>
                  </TableRow>
                </TableHeader>

                <TableBody>
                  {logsData.logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-mono text-sm">
                        {format.date(log.createdAt * 1000)}
                      </TableCell>
                      <TableCell>{log.username}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Icons.provider provider={log.provider} className="h-4 w-4" />
                          {LLM_PROVIDER_DISPLAY_NAME[log.provider]}
                        </div>
                      </TableCell>
                      <TableCell>{log.modelFamilyName}</TableCell>
                      <TableCell className="font-mono text-sm">{log.model}</TableCell>
                      <TableCell className="text-right font-mono">
                        {format.number(log.inputTokens)}
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {format.number(log.outputTokens)}
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {format.number(log.reasoningTokens)}
                      </TableCell>
                      <TableCell className="text-right font-mono font-semibold">
                        {format.number(log.totalTokens)}
                      </TableCell>
                      <TableCell className="text-right font-mono font-semibold">
                        {format.costShort(log.totalCost)}
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {log.keyHash.substring(0, 8)}...
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              <div className="flex items-center justify-between space-x-2 px-4 py-4">
                <div className="text-muted-foreground text-sm">
                  Showing {(logsData.pagination.page - 1) * logsData.pagination.pageSize + 1} to{" "}
                  {Math.min(
                    logsData.pagination.page * logsData.pagination.pageSize,
                    logsData.pagination.totalCount,
                  )}{" "}
                  of {logsData.pagination.totalCount} results
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={!logsData.pagination.hasPreviousPage}
                  >
                    Previous
                  </Button>

                  <div className="text-sm">
                    Page {logsData.pagination.page} of {logsData.pagination.totalPages}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={!logsData.pagination.hasNextPage}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
