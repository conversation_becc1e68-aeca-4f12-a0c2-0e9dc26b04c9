import { useQuery } from "@tanstack/react-query";
import {
  ActivityIcon,
  BarChart3Icon,
  ClockArrowDownIcon,
  ClockArrowUpIcon,
  CoinsIcon,
  DollarSignIcon,
  RefreshCwIcon,
} from "lucide-react";
import { useState } from "react";
import { Area, AreaChart, Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@/components/ui/chart";
import { Icons } from "@/components/ui/icons";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { useTRPC } from "@/lib/trpc/client";
import { LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";
import { format } from "@/shared/utils";

type SortOption = "oldest" | "newest" | "most_expensive" | "most_tokens";

// Chart configurations
const requestsChartConfig = {
  totalRequests: {
    label: "Requests",
    color: "hsl(var(--chart-1))",
  },
} satisfies ChartConfig;

const tokensChartConfig = {
  totalInputTokens: {
    label: "Input Tokens",
    color: "hsl(var(--chart-1))",
  },
  totalOutputTokens: {
    label: "Output Tokens",
    color: "hsl(var(--chart-2))",
  },
  totalReasoningTokens: {
    label: "Reasoning Tokens",
    color: "hsl(var(--chart-3))",
  },
} satisfies ChartConfig;

const costChartConfig = {
  totalCost: {
    label: "Cost",
    color: "hsl(var(--chart-4))",
  },
} satisfies ChartConfig;

export function AdminLogsPage() {
  const [selectedProvider, setSelectedProvider] = useState<LLM_Providers | "all">("all");
  const [selectedModelFamily, setSelectedModelFamily] = useState<string>("all");
  const [selectedApiKey, setSelectedApiKey] = useState<string>("all");
  const [sortBy, setSortBy] = useState<SortOption>("newest");
  const [currentPage, setCurrentPage] = useState(1);
  const [chartDays, setChartDays] = useState(30);
  const [showLogs, setShowLogs] = useState(false);

  const trpc = useTRPC();

  // Get filter options
  const { data: filterOptions } = useQuery(trpc.logs.getFilterOptions.queryOptions());

  // Get summary statistics
  const { data: summary } = useQuery(trpc.logs.getSummary.queryOptions());

  // Get daily usage for charts
  const { data: dailyUsage } = useQuery(
    trpc.logs.getDailyUsage.queryOptions({
      days: chartDays,
      provider: selectedProvider !== "all" ? selectedProvider : undefined,
      modelFamilyId: selectedModelFamily !== "all" ? selectedModelFamily : undefined,
    }),
  );

  // Get logs data
  const {
    data: logsData,
    isLoading,
    refetch,
  } = useQuery(
    trpc.logs.list.queryOptions({
      provider: selectedProvider !== "all" ? selectedProvider : undefined,
      modelFamilyId: selectedModelFamily !== "all" ? selectedModelFamily : undefined,
      keyHash: selectedApiKey !== "all" ? selectedApiKey : undefined,
      sortBy,
      page: currentPage,
      pageSize: 20,
    }),
  );

  const handleRefresh = () => {
    refetch();
  };

  const getSortIcon = (option: SortOption) => {
    switch (option) {
      case "oldest":
        return <ClockArrowUpIcon className="h-4 w-4" />;
      case "newest":
        return <ClockArrowDownIcon className="h-4 w-4" />;
      case "most_expensive":
        return <DollarSignIcon className="h-4 w-4" />;
      case "most_tokens":
        return <CoinsIcon className="h-4 w-4" />;
      default:
        return <ClockArrowDownIcon className="h-4 w-4" />;
    }
  };

  const getSortLabel = (option: SortOption) => {
    switch (option) {
      case "oldest":
        return "Oldest";
      case "newest":
        return "Newest";
      case "most_expensive":
        return "Most Expensive";
      case "most_tokens":
        return "Most Tokens";
      default:
        return "Newest";
    }
  };

  const formatCurrency = (value: number) => {
    return `$${value.toFixed(6)}`;
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-semibold">Your Activity</h1>
        <p className="text-muted-foreground text-sm">
          See how you've been using models on the proxy. Privacy focused.
        </p>
      </div>

      {/* Chart Controls */}
      <div className="flex items-center justify-center">
        <Select value={chartDays.toString()} onValueChange={(value) => setChartDays(Number(value))}>
          <SelectTrigger className="w-[140px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="14">Last 14 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="60">Last 60 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Compact Dashboard Cards */}
      {dailyUsage && dailyUsage.length > 0 && (
        <div className="mx-auto grid max-w-6xl grid-cols-1 gap-6 md:grid-cols-3">
          {/* Spend Card */}
          <Card className="bg-card border-border border">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-muted-foreground text-sm font-medium">Spend</CardTitle>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-4-4m4 4v-4m0 4h-4"
                    />
                  </svg>
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pb-4">
              <ChartContainer config={costChartConfig} className="h-[120px] w-full">
                <AreaChart data={dailyUsage}>
                  <Area
                    type="monotone"
                    dataKey="totalCost"
                    stroke="var(--color-totalCost)"
                    fill="var(--color-totalCost)"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ChartContainer>
              <div className="mt-2 flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Last day</span>
                <span className="font-medium">
                  {dailyUsage.length > 0
                    ? formatCurrency(dailyUsage[dailyUsage.length - 1]?.totalCost || 0)
                    : "$0"}
                </span>
                <span className="text-muted-foreground">Last week</span>
                <span className="font-medium">
                  {summary ? format.costShort(summary.totalCost) : "$0"}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Tokens Card */}
          <Card className="bg-card border-border border">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-muted-foreground text-sm font-medium">Tokens</CardTitle>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-4-4m4 4v-4m0 4h-4"
                    />
                  </svg>
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pb-4">
              <ChartContainer config={tokensChartConfig} className="h-[120px] w-full">
                <BarChart data={dailyUsage}>
                  <Bar
                    dataKey="totalInputTokens"
                    stackId="tokens"
                    fill="var(--color-totalInputTokens)"
                  />
                  <Bar
                    dataKey="totalOutputTokens"
                    stackId="tokens"
                    fill="var(--color-totalOutputTokens)"
                  />
                  <Bar
                    dataKey="totalReasoningTokens"
                    stackId="tokens"
                    fill="var(--color-totalReasoningTokens)"
                  />
                </BarChart>
              </ChartContainer>
              <div className="mt-2 flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Last day</span>
                <span className="font-medium">
                  {dailyUsage.length > 0
                    ? format.number(
                        (dailyUsage[dailyUsage.length - 1]?.totalInputTokens || 0) +
                          (dailyUsage[dailyUsage.length - 1]?.totalOutputTokens || 0) +
                          (dailyUsage[dailyUsage.length - 1]?.totalReasoningTokens || 0),
                      )
                    : "0"}
                </span>
                <span className="text-muted-foreground">Last week</span>
                <span className="font-medium">
                  {summary ? format.number(summary.totalTokens) : "0"}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Requests Card */}
          <Card className="bg-card border-border border">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-muted-foreground text-sm font-medium">
                  Requests
                </CardTitle>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-4-4m4 4v-4m0 4h-4"
                    />
                  </svg>
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pb-4">
              <ChartContainer config={requestsChartConfig} className="h-[120px] w-full">
                <AreaChart data={dailyUsage}>
                  <Area
                    type="monotone"
                    dataKey="totalRequests"
                    stroke="var(--color-totalRequests)"
                    fill="var(--color-totalRequests)"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ChartContainer>
              <div className="mt-2 flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Last day</span>
                <span className="font-medium">
                  {dailyUsage.length > 0
                    ? format.number(dailyUsage[dailyUsage.length - 1]?.totalRequests || 0)
                    : "0"}
                </span>
                <span className="text-muted-foreground">Last week</span>
                <span className="font-medium">
                  {summary ? format.number(summary.totalLogs) : "0"}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Show Logs Button */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          onClick={() => setShowLogs(!showLogs)}
          className="flex items-center gap-2"
        >
          {showLogs ? "Hide" : "Show"} Detailed Logs
          <svg
            className={`h-4 w-4 transition-transform ${showLogs ? "rotate-180" : ""}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </Button>
      </div>

      {/* Filters and Logs - Only show when showLogs is true */}
      {showLogs && (
        <div className="space-y-4">
          {/* Filters */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-wrap items-center gap-2">
              {/* Provider Filter */}
              <Select
                value={selectedProvider}
                onValueChange={(value) => setSelectedProvider(value as LLM_Providers | "all")}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Providers" />
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="all">All Providers</SelectItem>
                  {filterOptions?.providers.map((provider) => (
                    <SelectItem key={provider} value={provider}>
                      <div className="flex items-center gap-2">
                        <Icons.provider provider={provider} className="h-4 w-4" />
                        {LLM_PROVIDER_DISPLAY_NAME[provider]}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Model Family Filter */}
              <Select value={selectedModelFamily} onValueChange={setSelectedModelFamily}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="All Model Families" />
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="all">All Model Families</SelectItem>
                  {filterOptions?.modelFamilies.map((family) => (
                    <SelectItem key={family.id} value={family.id}>
                      <div className="flex items-center gap-2">
                        <Icons.provider provider={family.provider} className="h-4 w-4" />
                        {family.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* API Key Filter */}
              <Select value={selectedApiKey} onValueChange={setSelectedApiKey}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="All API Keys" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All API Keys</SelectItem>
                  {filterOptions?.apiKeys.map((key) => (
                    <SelectItem key={key.hash} value={key.hash}>
                      {key.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Sort Filter */}
              <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortOption)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  {(["newest", "oldest", "most_expensive", "most_tokens"] as const).map(
                    (option) => (
                      <SelectItem key={option} value={option}>
                        <div className="flex items-center gap-2">
                          {getSortIcon(option)}
                          {getSortLabel(option)}
                        </div>
                      </SelectItem>
                    ),
                  )}
                </SelectContent>
              </Select>

              {/* Refresh Button */}
              <Button variant="outline" onClick={handleRefresh} title="Refresh logs">
                <RefreshCwIcon className="h-4 w-4" />
              </Button>

              {/* Clear Filters Button */}
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedProvider("all");
                  setSelectedModelFamily("all");
                  setSelectedApiKey("all");
                  setSortBy("newest");
                  setCurrentPage(1);
                }}
                className={
                  selectedProvider !== "all" ||
                  selectedModelFamily !== "all" ||
                  selectedApiKey !== "all" ||
                  sortBy !== "newest"
                    ? ""
                    : "hidden"
                }
              >
                Clear Filters
              </Button>
            </div>
          </div>

          {/* Logs Table */}
          <Card className="p-0">
            <CardContent className="p-2">
              {isLoading ? (
                <div className="flex h-24 items-center justify-center">
                  <div className="text-muted-foreground">Loading logs...</div>
                </div>
              ) : !logsData?.logs.length ? (
                <div className="flex h-24 items-center justify-center">
                  <div className="text-muted-foreground">No logs found matching your filters.</div>
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Provider</TableHead>
                        <TableHead>Model Family</TableHead>
                        <TableHead>Model</TableHead>
                        <TableHead className="text-right">Input</TableHead>
                        <TableHead className="text-right">Output</TableHead>
                        <TableHead className="text-right">Reasoning</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                        <TableHead className="text-right">Cost</TableHead>
                        <TableHead>API Key</TableHead>
                      </TableRow>
                    </TableHeader>

                    <TableBody>
                      {logsData.logs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell className="font-mono text-sm">
                            {format.date(log.createdAt * 1000)}
                          </TableCell>
                          <TableCell>{log.username}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Icons.provider provider={log.provider} className="h-4 w-4" />
                              {LLM_PROVIDER_DISPLAY_NAME[log.provider]}
                            </div>
                          </TableCell>
                          <TableCell>{log.modelFamilyName}</TableCell>
                          <TableCell className="font-mono text-sm">{log.model}</TableCell>
                          <TableCell className="text-right font-mono">
                            {format.number(log.inputTokens)}
                          </TableCell>
                          <TableCell className="text-right font-mono">
                            {format.number(log.outputTokens)}
                          </TableCell>
                          <TableCell className="text-right font-mono">
                            {format.number(log.reasoningTokens)}
                          </TableCell>
                          <TableCell className="text-right font-mono font-semibold">
                            {format.number(log.totalTokens)}
                          </TableCell>
                          <TableCell className="text-right font-mono font-semibold">
                            {format.costShort(log.totalCost)}
                          </TableCell>
                          <TableCell className="font-mono text-sm">
                            {log.keyHash.substring(0, 8)}...
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  <div className="flex items-center justify-between space-x-2 px-4 py-4">
                    <div className="text-muted-foreground text-sm">
                      Showing {(logsData.pagination.page - 1) * logsData.pagination.pageSize + 1} to{" "}
                      {Math.min(
                        logsData.pagination.page * logsData.pagination.pageSize,
                        logsData.pagination.totalCount,
                      )}{" "}
                      of {logsData.pagination.totalCount} results
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={!logsData.pagination.hasPreviousPage}
                      >
                        Previous
                      </Button>

                      <div className="text-sm">
                        Page {logsData.pagination.page} of {logsData.pagination.totalPages}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={!logsData.pagination.hasNextPage}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
